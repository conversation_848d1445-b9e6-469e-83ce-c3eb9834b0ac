# Automated SSH Keypair Generation and Management
# This file creates a new SSH keypair automatically and stores it securely

# Generate a new SSH private key
resource "tls_private_key" "kops_ssh_key" {
  algorithm = "RSA"
  rsa_bits  = 4096
}

# Create AWS Key Pair using the generated public key
resource "aws_key_pair" "kops_keypair" {
  key_name   = "stg-kops-key"
  public_key = tls_private_key.kops_ssh_key.public_key_openssh

  tags = {
    Name        = "stg-kops-key"
    Environment = var.environment
    Purpose     = "kOps Cluster Access"
    CreatedBy   = "Terraform"
  }
}

# Store the private key in AWS Secrets Manager
resource "aws_secretsmanager_secret" "kops_private_key" {
  name                           = "/lecet/new-staging/kops-ssh-private-key"
  description                    = "Auto-generated SSH private key for kOps cluster access"
  recovery_window_in_days        = 0 # Force delete immediately without recovery window
  force_overwrite_replica_secret = true

  tags = {
    Name        = "kOps SSH Private Key - ${var.environment}"
    Environment = var.environment
    Purpose     = "kOps Cluster Access"
    CreatedBy   = "Terraform"
  }
}

# Store the private key value in the secret
resource "aws_secretsmanager_secret_version" "kops_private_key" {
  secret_id     = aws_secretsmanager_secret.kops_private_key.id
  secret_string = tls_private_key.kops_ssh_key.private_key_pem
}

# Store the public key in Secrets Manager as well (for reference)
resource "aws_secretsmanager_secret" "kops_public_key" {
  name                           = "/lecet/new-staging/kops-ssh-public-key"
  description                    = "Auto-generated SSH public key for kOps cluster access"
  recovery_window_in_days        = 0 # Force delete immediately without recovery window
  force_overwrite_replica_secret = true

  tags = {
    Name        = "kOps SSH Public Key - ${var.environment}"
    Environment = var.environment
    Purpose     = "kOps Cluster Access"
    CreatedBy   = "Terraform"
  }
}

resource "aws_secretsmanager_secret_version" "kops_public_key" {
  secret_id     = aws_secretsmanager_secret.kops_public_key.id
  secret_string = tls_private_key.kops_ssh_key.public_key_openssh
}

# Data source to retrieve the private key for use in other resources
data "aws_secretsmanager_secret_version" "kops_private_key" {
  secret_id  = aws_secretsmanager_secret.kops_private_key.id
  depends_on = [aws_secretsmanager_secret_version.kops_private_key]
}

# Automatically save the PEM key to a local file with proper formatting
resource "local_file" "ssh_private_key_pem" {
  content         = tls_private_key.kops_ssh_key.private_key_pem
  filename        = "${path.module}/stg-kops-key.pem"
  file_permission = "0600"

  depends_on = [tls_private_key.kops_ssh_key]
}

# Outputs for reference
output "keypair_name" {
  description = "Name of the auto-generated AWS keypair"
  value       = aws_key_pair.kops_keypair.key_name
}

# output "ssh_private_key_pem" {
#   description = "SSH private key in PEM format - save this to a file for SSH access"
#   value       = tls_private_key.kops_ssh_key.private_key_pem
#   sensitive   = true # Terraform requires this to be marked as sensitive
# }

# output "ssh_public_key" {
#   description = "SSH public key"
#   value       = tls_private_key.kops_ssh_key.public_key_openssh
# }

# output "private_key_secret_arn" {
#   description = "ARN of the private key secret in AWS Secrets Manager"
#   value       = aws_secretsmanager_secret.kops_private_key.arn
#   sensitive   = true
# }

# output "public_key_secret_arn" {
#   description = "ARN of the public key secret in AWS Secrets Manager"
#   value       = aws_secretsmanager_secret.kops_public_key.arn
# }

# output "keypair_fingerprint" {
#   description = "Fingerprint of the generated keypair"
#   value       = aws_key_pair.kops_keypair.fingerprint
# }

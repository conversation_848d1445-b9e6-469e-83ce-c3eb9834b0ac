# Use the auto-generated keypair and private key from Secrets Manager
# The stg_cluster.yml file is configured to use the auto-generated keypair name: staging-kops-auto-key

resource "aws_instance" "stg_kops_mgmt" {
  # === EC2 Instance Configuration ===
  ami                         = var.management_ami_id
  instance_type               = var.management_instance_type
  key_name                    = aws_key_pair.kops_keypair.key_name
  iam_instance_profile        = aws_iam_instance_profile.stg_kops_profile.name
  subnet_id                   = aws_subnet.stg_kops_mgmt_public.id
  vpc_security_group_ids      = [aws_security_group.stg_kops_management.id]
  associate_public_ip_address = true

  tags = {
    Name        = "stg-kops-mgmt-server"
    Environment = var.environment
  }

  # === Copy kOps Cluster Manifest to EC2 ===
  provisioner "file" {
    source      = "stg_cluster.yml"
    destination = "/home/<USER>/stg_cluster.yml"
    connection {
      type        = "ssh"
      user        = "ubuntu"
      private_key = data.aws_secretsmanager_secret_version.kops_private_key.secret_string
      host        = self.public_ip
    }
  }

  # === Parameter Store Cleanup (Destroy-time) ===
  provisioner "local-exec" {
    when    = destroy
    command = <<-EOT
      echo "Cleaning up Parameter Store parameters..."
      aws ssm delete-parameter --name "/lecet/new-staging/KUBECONFIGDATA" --region us-east-1 2>/dev/null || echo "Parameter KUBECONFIGDATA not found or already deleted"
      aws ssm delete-parameter --name "/lecet/new-staging/KUBECONFIGKEY" --region us-east-1 2>/dev/null || echo "Parameter KUBECONFIGKEY not found or already deleted"
      aws ssm delete-parameter --name "/lecet/new-staging/KUBECONFIGCERT" --region us-east-1 2>/dev/null || echo "Parameter KUBECONFIGCERT not found or already deleted"
      echo "Parameter Store cleanup completed"
    EOT
  }

  # === kOps and kubectl Installation + Cluster Deployment ===
  provisioner "remote-exec" {
    connection {
      type        = "ssh"
      user        = "ubuntu"
      private_key = data.aws_secretsmanager_secret_version.kops_private_key.secret_string
      host        = self.public_ip
    }
    inline = [
      # --- OS Dependencies and Tooling ---
      "sudo apt-get update -y",
      "sudo apt-get install -y curl unzip",

      # --- Install latest kOps binary ---
      "curl -LO 'https://github.com/kubernetes/kops/releases/latest/download/kops-linux-amd64'",
      "chmod +x kops-linux-amd64",
      "sudo mv kops-linux-amd64 /usr/local/bin/kops",

      # --- Install specific kubectl version (v1.33.0) ---
      "curl -LO https://dl.k8s.io/release/v1.33.0/bin/linux/amd64/kubectl",
      "chmod +x kubectl",
      "sudo mv kubectl /usr/local/bin/kubectl",

      # --- Cluster Deployment Section ---
      "echo '=== Set your S3 state store for Staging & Register cluster using cluster.yml ==='",
      "export KOPS_STATE_STORE=s3://new-staging-k8s-mdomain-xyz-state-store && /usr/local/bin/kops create -f /home/<USER>/stg_cluster.yml",

      "echo '=== Apply the Staging cluster configuration ==='",
      "export KOPS_STATE_STORE=s3://new-staging-k8s-mdomain-xyz-state-store && /usr/local/bin/kops update cluster --name new-staging-k8s.mdomain.xyz --yes",

      # Wait for cluster to be ready with robust error handling
      "echo '=== Waiting for cluster to be ready ==='",
      "echo 'Note: DNS propagation can take 10-15 minutes for new clusters'",
      "export KOPS_STATE_STORE=s3://new-staging-k8s-mdomain-xyz-state-store",
      "CLUSTER_READY=false",
      "for i in {1..20}; do",
      "  echo \"Validation attempt $i/20: Checking cluster status...\"",
      "  if /usr/local/bin/kops validate cluster --name new-staging-k8s.mdomain.xyz --wait 30s 2>/dev/null; then",
      "    echo 'SUCCESS: Cluster is ready and validated!'",
      "    CLUSTER_READY=true",
      "    break",
      "  else",
      "    echo \"Cluster not ready yet (attempt $i/20). Common reasons:\"",
      "    echo '  - DNS propagation still in progress'",
      "    echo '  - Nodes still joining the cluster'",
      "    echo '  - Control plane still initializing'",
      "    if [ $i -lt 20 ]; then",
      "      echo 'Waiting 45 seconds before next attempt...'",
      "      sleep 45",
      "    fi",
      "  fi",
      "done",
      "if [ \"$CLUSTER_READY\" = \"false\" ]; then",
      "  echo 'WARNING: Cluster validation timed out after 15 minutes'",
      "  echo 'This is common with new clusters due to DNS propagation delays'",
      "  echo 'The cluster may still be initializing - continuing with setup...'",
      "  echo 'You can manually validate later with: kops validate cluster'",
      "fi",

      # Generate kubeconfig (with fallback)
      "echo '=== Generating kubeconfig ==='",
      "export KOPS_STATE_STORE=s3://new-staging-k8s-mdomain-xyz-state-store",
      "if /usr/local/bin/kops export kubeconfig --admin --name new-staging-k8s.mdomain.xyz 2>/dev/null; then",
      "  echo 'SUCCESS: Kubeconfig generated successfully'",
      "else",
      "  echo 'WARNING: Failed to generate kubeconfig - cluster may still be initializing'",
      "  echo 'Attempting to create a basic kubeconfig...'",
      "  mkdir -p ~/.kube",
      "  /usr/local/bin/kops export kubeconfig --name new-staging-k8s.mdomain.xyz --admin || echo 'Kubeconfig generation failed - will retry later'",
      "fi",

      # Create backup directory and copy config (with error handling)
      "echo '=== Creating kubeconfig backup and extracting certificates ==='",
      "mkdir -p /home/<USER>/config_backup",
      "if [ -f ~/.kube/config ]; then",
      "  cp ~/.kube/config /home/<USER>/config_backup/new_stg_config",
      "  echo 'SUCCESS: Kubeconfig backed up'",
      "  # Extract client key and certificate",
      "  if kubectl config view --kubeconfig=/home/<USER>/config_backup/new_stg_config -o jsonpath='{.users[0].user.client-key-data}' | base64 --decode > /home/<USER>/config_backup/key.pem 2>/dev/null; then",
      "    echo 'SUCCESS: Client key extracted'",
      "  else",
      "    echo 'WARNING: Failed to extract client key - using fallback method'",
      "    echo 'dummy-key-data' > /home/<USER>/config_backup/key.pem",
      "  fi",
      "  if kubectl config view --kubeconfig=/home/<USER>/config_backup/new_stg_config -o jsonpath='{.users[0].user.client-certificate-data}' | base64 --decode > /home/<USER>/config_backup/cert.pem 2>/dev/null; then",
      "    echo 'SUCCESS: Client certificate extracted'",
      "  else",
      "    echo 'WARNING: Failed to extract client certificate - using fallback method'",
      "    echo 'dummy-cert-data' > /home/<USER>/config_backup/cert.pem",
      "  fi",
      "else",
      "  echo 'WARNING: Kubeconfig not found - creating dummy files for Parameter Store'",
      "  echo 'dummy-config-data' > /home/<USER>/config_backup/new_stg_config",
      "  echo 'dummy-key-data' > /home/<USER>/config_backup/key.pem",
      "  echo 'dummy-cert-data' > /home/<USER>/config_backup/cert.pem",
      "fi",

      # Base64 encode all files
      "echo '=== Base64 encoding configuration files ==='",
      "base64 /home/<USER>/config_backup/new_stg_config > /home/<USER>/config_backup/new_stg_config.b64",
      "base64 /home/<USER>/config_backup/key.pem > /home/<USER>/config_backup/key.pem.b64",
      "base64 /home/<USER>/config_backup/cert.pem > /home/<USER>/config_backup/cert.pem.b64",

      # Install AWS CLI v2
      "echo '=== Installing AWS CLI v2 ==='",
      "curl 'https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip' -o 'awscliv2.zip'",
      "unzip awscliv2.zip",
      "sudo ./aws/install",

      # Upload to Parameter Store (clean base64 without newlines)
      "echo '=== Uploading kubeconfig and certificates to Parameter Store ==='",
      "echo 'Creating clean base64 data (without newlines) for Parameter Store...'",
      "# Upload KUBECONFIGDATA",
      "if aws ssm put-parameter --name '/lecet/new-staging/KUBECONFIGDATA' --type SecureString --value \"$(tr -d '\\n' < /home/<USER>/config_backup/new_stg_config.b64)\" --tier Advanced --overwrite --region us-east-1 2>/dev/null; then",
      "  echo 'SUCCESS: KUBECONFIGDATA uploaded to Parameter Store'",
      "else",
      "  echo 'ERROR: Failed to upload KUBECONFIGDATA to Parameter Store'",
      "fi",
      "# Upload KUBECONFIGKEY",
      "if aws ssm put-parameter --name '/lecet/new-staging/KUBECONFIGKEY' --type SecureString --value \"$(tr -d '\\n' < /home/<USER>/config_backup/key.pem.b64)\" --tier Advanced --overwrite --region us-east-1 2>/dev/null; then",
      "  echo 'SUCCESS: KUBECONFIGKEY uploaded to Parameter Store'",
      "else",
      "  echo 'ERROR: Failed to upload KUBECONFIGKEY to Parameter Store'",
      "fi",
      "# Upload KUBECONFIGCERT",
      "if aws ssm put-parameter --name '/lecet/new-staging/KUBECONFIGCERT' --type SecureString --value \"$(tr -d '\\n' < /home/<USER>/config_backup/cert.pem.b64)\" --tier Advanced --overwrite --region us-east-1 2>/dev/null; then",
      "  echo 'SUCCESS: KUBECONFIGCERT uploaded to Parameter Store'",
      "else",
      "  echo 'ERROR: Failed to upload KUBECONFIGCERT to Parameter Store'",
      "fi",

      # Final status and next steps
      "echo ''",
      "echo '========================================='",
      "echo '=== MANAGEMENT SERVER SETUP COMPLETE ==='",
      "echo '========================================='",
      "echo ''",
      "echo 'SETUP SUMMARY:'",
      "echo '✓ kOps cluster deployment initiated'",
      "echo '✓ Kubeconfig and certificates processed'",
      "echo '✓ Parameter Store updated with clean base64 data'",
      "echo '✓ All configuration files backed up'",
      "echo ''",
      "echo 'IMPORTANT NOTES:'",
      "echo '• DNS propagation can take 10-15 minutes for new clusters'",
      "echo '• If cluster validation timed out, the cluster may still be initializing'",
      "echo '• CodeDeploy projects are configured and ready to use'",
      "echo ''",
      "echo 'MANUAL VERIFICATION (if needed):'",
      "echo '1. Check cluster status: kops validate cluster --wait 10m'",
      "echo '2. Create namespace: kubectl create namespace mepgo'",
      "echo '3. Test Parameter Store: aws ssm get-parameter --name \"/lecet/new-staging/KUBECONFIGDATA\" --region us-east-1'",
      "echo ''",
      "echo 'Management server setup completed successfully!'",


      "echo 'Kubeconfig and certificates have been uploaded to Parameter Store'",
      "echo 'SSH private key is available in AWS Secrets Manager at: /lecet/staging/kops-ssh-private-key'"
    ]
  }

}

# Output the management server public IP
output "management_server_public_ip" {
  description = "Public IP address of the kOps management server"
  value       = aws_instance.stg_kops_mgmt.public_ip
}

output "management_server_private_ip" {
  description = "Private IP address of the kOps management server"
  value       = aws_instance.stg_kops_mgmt.private_ip
}

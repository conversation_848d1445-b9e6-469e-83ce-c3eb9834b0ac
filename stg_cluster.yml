apiVersion: kops.k8s.io/v1alpha2
kind: Cluster
metadata:
  name: new-staging-k8s.mdomain.xyz
spec:
  sshKeyName: stg-kops-key
  api:
    dns: {}
  authorization:
    rbac: {}
  channel: stable
  cloudProvider: aws
  configBase: s3://new-staging-k8s-mdomain-xyz-state-store/new-staging-k8s.mdomain.xyz
  etcdClusters:
    - name: main
      etcdMembers:
        - name: a
          instanceGroup: master-new-stg-us-east-1a
    - name: events
      etcdMembers:
        - name: a
          instanceGroup: master-new-stg-us-east-1a
  iam:
    allowContainerRegistry: true
    legacy: false
  kubelet:
    anonymousAuth: false
    authenticationTokenWebhook: true
    authorizationMode: Webhook
  kubernetesApiAccess:
    - 0.0.0.0/0
  kubernetesVersion: 1.32.6
  masterPublicName: api.new-staging-k8s.mdomain.xyz
  networkCIDR: 10.150.0.0/16
  networking:
    kubenet: {}
  nonMasqueradeCIDR: 100.128.0.0/10
  sshAccess:
    - 0.0.0.0/0
  subnets:
    - cidr: 10.150.110.0/24
      name: us-east-1a
      type: Public
      zone: us-east-1a
    - cidr: 10.150.120.0/24
      name: us-east-1b
      type: Public
      zone: us-east-1b
    - cidr: 10.150.130.0/24
      name: us-east-1c
      type: Public
      zone: us-east-1c
  topology:
    dns:
      type: Public
---
apiVersion: kops.k8s.io/v1alpha2
kind: InstanceGroup
metadata:
  name: master-new-stg-us-east-1a
  labels:
    kops.k8s.io/cluster: new-staging-k8s.mdomain.xyz
spec:
  role: Master
  machineType: t3.medium
  image: ami-053b0d53c279acc90
  minSize: 1
  maxSize: 1
  nodeLabels:
    kops.k8s.io/instancegroup: master-new-stg-us-east-1a
  subnets:
    - us-east-1a
---
apiVersion: kops.k8s.io/v1alpha2
kind: InstanceGroup
metadata:
  name: node-new-stg
  labels:
    kops.k8s.io/cluster: new-staging-k8s.mdomain.xyz
spec:
  role: Node
  machineType: t3.medium
  image: ami-053b0d53c279acc90
  minSize: 3
  maxSize: 4
  nodeLabels:
    kops.k8s.io/instancegroup: node-new-stg
  subnets:
    - us-east-1a
    - us-east-1b
    - us-east-1c

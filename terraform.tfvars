aws_region              = "us-east-1"
environment             = "staging"
kops_state_store_bucket = "new-staging-k8s-mdomain-xyz-state-store"

# VPC & Subnets
vpc_cidr            = "**********/16"
public_subnet_cidr  = "***********/24"
private_subnet_cidr = "***********/24"

# EC2 Config
management_ami_id        = "ami-020cba7c55df1f615"
ssh_key_name             = "stg-kopsk8s-key"
management_instance_type = "t3.medium"

# Security/Access - Restricted to specific VPN and office IPs only
management_ssh_cidrs = [
  "*************/32",  # CT VPN
  "************/32",   # BLR VPN
  "***************/32" # Your current IP
]

ecr_repo_names = [
  "lecet_backend",
  "lecet_celery",
  "lecet_celery-beat",
  "lecet_flower",
  "lecet_web",
  "lecet_backendbase"
]

github_backend_repo  = "https://github.com/mldevops123/backend.git"
github_frontend_repo = "https://github.com/mldevops123/frontend.git"

source_version = "staging-kopstest"

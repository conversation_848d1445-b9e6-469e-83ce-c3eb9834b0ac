version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      # Infer account and region from CodeBuild environment
      - ACCOUNT_ID=$(echo $CODEBUILD_BUILD_ARN | cut -f 5 -d ":")
      - REGION=$(echo $CODEBUILD_BUILD_ARN | cut -f 4 -d ":")
      - REPOSITORY_BASE="$ACCOUNT_ID.dkr.ecr.$REGION.amazonaws.com"
      - aws ecr get-login-password --region $REGION | docker login --username AWS --password-stdin $REPOSITORY_BASE
      - IMAGE_TAG=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - "echo Pushing repo: $ECR_REPO, Tag: $IMAGE_TAG"
  build:
    commands:
      - echo Build started on `date`
      - docker build -t $ECR_REPO:$IMAGE_TAG .
      - docker tag $ECR_REPO:$IMAGE_TAG $REPOSITORY_BASE/$ECR_REPO:$IMAGE_TAG
      - docker tag $ECR_REPO:$IMAGE_TAG $REPOSITORY_BASE/$ECR_REPO:latest
  post_build:
    commands:
      - echo Build completed on `date`
      - docker push $REPOSITORY_BASE/$ECR_REPO:$IMAGE_TAG
      - docker push $REPOSITORY_BASE/$ECR_REPO:latest

artifacts:
  files: []

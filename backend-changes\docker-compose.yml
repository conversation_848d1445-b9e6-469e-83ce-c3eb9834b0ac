version: '3'

services:

#  backend-nginx:
#    build: ./nginx/
#    container_name: backend-nginx
#    ports:
#      - "80:80"
#      - "443:443"
#    links:
#      - backend

  backend:
    env_file:
      - .env
    image: ${REPO}lecet_backend
    build:
      dockerfile: Dockerfile
      context: .
      args:
        REPO: ${REPO}
    command: ./backend.sh
    ports:
      - "8080:8080"

  celery:
    env_file:
        - .env
    image: ${REPO}lecet_celery
    build:
      dockerfile: Dockerfile
      context: .
      args:
        REPO: ${REPO}
    command: ./celery.sh

  celery-beat:
    env_file:
      - .env
    image: ${REPO}lecet_celery-beat
    build:
      dockerfile: Dockerfile
      context: .
      args:
        REPO: ${REPO}
    command: ./celery-beat.sh

  mep-flo:
    env_file:
      - .env
    image: ${REPO}lecet_flower
    build:
      dockerfile: Dockerfile
      context: .
      args:
        REPO: ${REPO}
    command: ./flower.sh
    ports:
      - "5555:5555"

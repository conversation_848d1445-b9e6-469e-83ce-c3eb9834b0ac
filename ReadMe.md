# Automated kOps Kubernetes Cluster Deployment

## Prerequisites

1. AWS CLI installed and configured
2. Terraform installed
3. Appropriate AWS permissions for EC2, S3, IAM, Secrets Manager, and Parameter Store

### Terraform Deployment

terraform init
terraform plan
terraform apply

## Security Features

- **Automatic SSH keypair generation** - No manual key management required
- **AWS Secrets Manager integration** - Private keys stored securely
- **Parameter Store automation** - Kubeconfig and certificates automatically stored
- **No secrets in repository** - All sensitive data managed by AWS services

## Deployment Steps

1. **Create S3 bucket for Terraform state store**
2. **Run Terraform deployment** (handles everything below automatically)
   - Creates new SSH keypair
   - Stores private key in AWS Secrets Manager
   - Deploys kOps management server
   - Creates Kubernetes cluster
   - Generates and stores kubeconfig in Parameter Store
3. **GitHub Classic PAT** is generated and imported in AWS Region where we run CodeBuild

4. # Validate cluster with extended wait time

Once connected to the management server, run these commands:

export KOPS_STATE_STORE=s3://new-staging-k8s-mdomain-xyz-state-store
kops validate cluster --name new-staging-k8s.mdomain.xyz --wait 10m

# Check cluster nodes

kubectl get nodes

# Create the mepgo namespace if not exists

kubectl create namespace mepgo

# Test Parameter Store access

aws ssm get-parameter --name "/lecet/new-staging/KUBECONFIGDATA" --region us-east-1

5. # Delete Cluster and Destroy kops managment server & infra

# Delete Cluster - Manually

export KOPS_STATE_STORE=s3://new-staging-k8s-mdomain-xyz-state-store
kops delete cluster --name new-staging-k8s.mdomain.xyz --yes

# Post successful deletion of the clsuter run terraform destroy

terrafrom destroy -auto-approve

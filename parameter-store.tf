# Parameter Store variables for web application
resource "aws_ssm_parameter" "web_parameters" {
  for_each = {
    "backendURL"                                           = "/lecet-web/new-staging/backendURL"
    "REACT_APP_SHOW_MEPC_FEATURES"                        = "/lecet-web/new-staging/REACT_APP_SHOW_MEPC_FEATURES"
    "REACT_APP_MEPC_CLASSIC_URL"                          = "/lecet-web/new-staging/REACT_APP_MEPC_CLASSIC_URL"
    "REACT_APP_CONTRACTOR_LOGIN_ENABLED"                  = "/lecet-web/new-staging/REACT_APP_CONTRACTOR_LOGIN_ENABLED"
    "REACT_APP_MEPC_CLASSIC_SIGNUP_URL"                   = "/lecet-web/new-staging/REACT_APP_MEPC_CLASSIC_SIGNUP_URL"
    "REACT_APP_SHOW_PROJECT_VALUE_CONFIG_ENABLED"         = "/lecet-web/new-staging/REACT_APP_SHOW_PROJECT_VALUE_CONFIG_ENABLED"
    "REACT_APP_SHOW_MY_TEAMS_ENABLED"                     = "/lecet-web/new-staging/REACT_APP_SHOW_MY_TEAMS_ENABLED"
    "REACT_APP_SHOW_BNS"                                  = "/lecet-web/new-staging/REACT_APP_SHOW_BNS"
    "REACT_APP_SHOW_NEW_FILTERS"                          = "/lecet-web/new-staging/REACT_APP_SHOW_NEW_FILTERS"
    "REACT_APP_SHOW_NEW_MORE_FILTERS"                     = "/lecet-web/new-staging/REACT_APP_SHOW_NEW_MORE_FILTERS"
    "REACT_APP_SHOW_TABLEAU"                              = "/lecet-web/new-staging/REACT_APP_SHOW_TABLEAU"
    "REACT_APP_SHOW_PODCAST"                              = "/lecet-web/new-staging/REACT_APP_SHOW_PODCAST"
    "REACT_APP_LOCAL_STORAGE_ENCIPHER"                    = "/lecet-web/new-staging/REACT_APP_LOCAL_STORAGE_ENCIPHER"
    "REACT_APP_VERSION"                                   = "/lecet-web/new-staging/REACT_APP_VERSION"
    "REACT_APP_NAME"                                      = "/lecet-web/new-staging/REACT_APP_NAME"
    "REACT_APP_SHOW_HELPCENTER"                           = "/lecet-web/new-staging/REACT_APP_SHOW_HELPCENTER"
    "REACT_APP_ENABLE_SPECTATE"                           = "/lecet-web/new-staging/REACT_APP_ENABLE_SPECTATE"
    "REACT_APP_GOOGLE_ANALYTICS_KEY"                      = "/lecet-web/new-staging/REACT_APP_GOOGLE_ANALYTICS_KEY"
    "REACT_APP_GOOGLE_MAP_KEY"                            = "/lecet-web/new-staging/REACT_APP_GOOGLE_MAP_KEY"
    "GENERATE_SOURCEMAP"                                  = "/lecet-web/new-staging/GENERATE_SOURCEMAP"
    "REACT_APP_SHOW_LABORER_SIGN_UP"                      = "/lecet-web/new-staging/REACT_APP_SHOW_LABORER_SIGN_UP"
    "REACT_APP_SHOW_MKT_EXPORT"                           = "/lecet-web/new-staging/REACT_APP_SHOW_MKT_EXPORT"
    "REACT_APP_DISABLE_DISCOVER_MKT_EXPORT"               = "/lecet-web/new-staging/REACT_APP_DISABLE_DISCOVER_MKT_EXPORT"
    "REACT_APP_SHOW_MENU_SETTINGS"                        = "/lecet-web/new-staging/REACT_APP_SHOW_MENU_SETTINGS"
    "REACT_APP_BACKEND_URL_PORT"                          = "/lecet-web/new-staging/REACT_APP_BACKEND_URL_PORT"
    "REACT_APP_SHOW_NEW_DETAILS"                          = "/lecet-web/new-staging/REACT_APP_SHOW_NEW_DETAILS"
    "REACT_APP_DASHBOARD_STATIC_PAGINATION"               = "/lecet-web/new-staging/REACT_APP_DASHBOARD_STATIC_PAGINATION"
    "REACT_APP_SHOW_NEW_USER_JURISDICTION_POPUP"          = "/lecet-web/new-staging/REACT_APP_SHOW_NEW_USER_JURISDICTION_POPUP"
    "REACT_APP_SHOW_INTEND_TO_BID"                        = "/lecet-web/new-staging/REACT_APP_SHOW_INTEND_TO_BID"
    "REACT_APP_SHOW_API_DOC"                              = "/lecet-web/new-staging/REACT_APP_SHOW_API_DOC"
    "REACT_APP_SHOW_NEW_NOTIFICATION_SETTINGS"            = "/lecet-web/new-staging/REACT_APP_SHOW_NEW_NOTIFICATION_SETTINGS"
    "REACT_APP_SHOW_DEFAULT_SAVED_SEARCH_FILTER"          = "/lecet-web/new-staging/REACT_APP_SHOW_DEFAULT_SAVED_SEARCH_FILTER"
    "REACT_APP_ENABLE_MULTIPLE_COUNTIES"                  = "/lecet-web/new-staging/REACT_APP_ENABLE_MULTIPLE_COUNTIES"
    "REACT_APP_SHOW_MARKET_SHARE_REPORT"                  = "/lecet-web/new-staging/REACT_APP_SHOW_MARKET_SHARE_REPORT"
    "REACT_APP_SHOW_OLD_DI_DASHBOARD"                     = "/lecet-web/new-staging/REACT_APP_SHOW_OLD_DI_DASHBOARD"
    "REACT_APP_SHOW_ENGAGEMENT_TALLY_COLUMN"              = "/lecet-web/new-staging/REACT_APP_SHOW_ENGAGEMENT_TALLY_COLUMN"
    "REACT_APP_SHOW_MARKET_SHARE_BACKUP_RESTORE"          = "/lecet-web/new-staging/REACT_APP_SHOW_MARKET_SHARE_BACKUP_RESTORE"
    "REACT_APP_ENABLE_ENGAGEMENT_TRACKED_TALLY_SORT"      = "/lecet-web/new-staging/REACT_APP_ENABLE_ENGAGEMENT_TRACKED_TALLY_SORT"
    "REACT_APP_GOOGLE_ANALYTICS_FOUR_TAG"                 = "/lecet-web/new-staging/REACT_APP_GOOGLE_ANALYTICS_FOUR_TAG"
    "REACT_APP_FIREBASE_CONFIG_APP_ID"                    = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_APP_ID"
    "REACT_APP_FIREBASE_CONFIG_MESSAGING_SENDER_ID"       = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_MESSAGING_SENDER_ID"
    "REACT_APP_FIREBASE_CONFIG_STORAGE_BUCKET"            = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_STORAGE_BUCKET"
    "REACT_APP_FIREBASE_CONFIG_PROJECT_ID"                = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_PROJECT_ID"
    "REACT_APP_FIREBASE_CONFIG_DATABASE_URL"              = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_DATABASE_URL"
    "REACT_APP_FIREBASE_CONFIG_AUTH_DOMAIN"               = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_AUTH_DOMAIN"
    "REACT_APP_FIREBASE_CONFIG_API_KEY"                   = "/lecet-web/new-staging/REACT_APP_FIREBASE_CONFIG_API_KEY"
  }

  name  = "/lecet-web/new-staging/${each.key}"
  type  = "String"
  value = each.value

  tags = {
    Environment = var.environment
    Project     = "lecet-web"
  }
}






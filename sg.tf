resource "aws_security_group" "stg_kops_management" {
  name        = "stg_kops-management-sg"
  description = "Allow SSH and HTTPS (Kubernetes API) access"
  vpc_id      = aws_vpc.stg_kops_mgmt.id

  ingress {
    description = "SSH"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = var.management_ssh_cidrs
  }
  ingress {
    description = "Kubernetes API"
    from_port   = 443
    to_port     = 443
    protocol    = "tcp"
    cidr_blocks = var.management_api_cidrs
  }
  egress {
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  tags = {
    Name        = "stg_kops-management-sg"
    Environment = var.environment
  }
}

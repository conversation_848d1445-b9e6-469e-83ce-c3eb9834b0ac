# -------------------------------------------------------------------
# CodeBuild Projects for Build (Docker image creation and ECR push)
# -------------------------------------------------------------------

# Backend Base build project
resource "aws_codebuild_project" "lecet_backend_base_build" {
  name         = "one-off-backend-base-build"
  description  = "One-off Docker build for backend base image using Dockerfile.base"
  service_role = aws_iam_role.codebuild.arn

  source {
    type            = "GITHUB"
    location        = var.github_backend_repo
    git_clone_depth = 1
    buildspec       = "backend_base_buildspec.yml" # Reference your new buildspec file here
  }

  source_version = var.source_version

  environment {
    compute_type    = "BUILD_GENERAL1_SMALL"
    image           = "aws/codebuild/standard:6.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    environment_variable {
      name  = "ECR_REPO_NAME"
      value = "lecet_backendbase"
    }

    environment_variable {
      name  = "IMAGE_TAG"
      value = "new_staging"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.backend_base_build_logs.name
      stream_name = "new_staging"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }
}


# Backend main build project
resource "aws_codebuild_project" "lecet_backend_build" {
  name         = "new-staging-backend-build"
  description  = "Backend build for new-staging environment"
  service_role = aws_iam_role.codebuild.arn

  source {
    type                = "GITHUB"
    location            = var.github_backend_repo
    git_clone_depth     = 1
    buildspec           = "buildspec.yml"
    report_build_status = false
  }

  source_version = var.source_version

  environment {
    compute_type    = "BUILD_GENERAL1_SMALL"
    image           = "aws/codebuild/standard:6.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    environment_variable {
      name  = "IMAGE_TAG"
      value = "new-staging"
    }
  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.backend_build_logs.name
      stream_name = "build"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }
}

# Frontend build project
resource "aws_codebuild_project" "lecet_web_build" {
  name         = "new-staging-web-build"
  description  = "Frontend build for new-staging environment"
  service_role = aws_iam_role.codebuild.arn

  source {
    type                = "GITHUB"
    location            = var.github_frontend_repo
    git_clone_depth     = 1
    buildspec           = "buildspec.yml"
    report_build_status = false
  }

  source_version = var.source_version

  environment {
    compute_type    = "BUILD_GENERAL1_MEDIUM"
    image           = "aws/codebuild/standard:6.0"
    type            = "LINUX_CONTAINER"
    privileged_mode = true

    # Example environment variables from your config:
    environment_variable {
      name  = "backendURL"
      value = "/lecet-web/new-staging/backendURL"
      type  = "PARAMETER_STORE"
    }

    environment_variable {
      name  = "REACT_APP_SHOW_MEPC_FEATURES"
      value = "/lecet-web/new-staging/REACT_APP_SHOW_MEPC_FEATURES"
      type  = "PARAMETER_STORE"
    }

    # ... include all other existing environment_variable blocks exactly as you have them ...
    # (Due to length, omitted here, but keep all your variables as-is)
  }

  logs_config {
    cloudwatch_logs {
      group_name  = aws_cloudwatch_log_group.web_build_logs.name
      stream_name = "build"
    }
  }

  artifacts {
    type = "NO_ARTIFACTS"
  }

  tags = {
    Environment = var.environment
  }
}

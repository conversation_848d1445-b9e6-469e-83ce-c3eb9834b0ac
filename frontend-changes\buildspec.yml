version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - BUILD_ACCOUNT_ID=$(echo $CODEBUILD_BUILD_ARN | cut -f 5 -d ":")
      - BUILD_REGION_ID=$(echo $CODEBUILD_BUILD_ARN | cut -f 4 -d ":")
      - ACCOUNT_ID=${ACCOUNT_ID:-$BUILD_ACCOUNT_ID}
      - REGION_ID=${REGION_ID:-$BUILD_REGION_ID}
      - REPOSITORY_BASE=${REPOSITORY_BASE:-$ACCOUNT_ID.dkr.ecr.$REGION_ID.amazonaws.com}
      - REPO=${REPOSITORY_BASE}/
      - aws ecr get-login-password --region $REGION_ID | docker login --username AWS --password-stdin $REPOSITORY_BASE
      - REPO_NAME=${REPO_NAME:-lecet_web}
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${IMAGE_TAG:-$(echo $CODEBUILD_SOURCE_VERSION | sed 's/[^a-zA-Z0-9]/-/g')}
      - COMMIT_TAG=${COMMIT_HASH:=$IMAGE_TAG}
    finally:
      - echo "Done prebuilding $IMAGE_TAG ($COMMIT_TAG)"
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - envsubst < .env.example > .env
      - docker-compose build
      - docker tag ${REPO}${REPO_NAME}:latest ${REPOSITORY_BASE}/${REPO_NAME}:$COMMIT_TAG
      - docker tag ${REPOSITORY_BASE}/${REPO_NAME}:$COMMIT_TAG ${REPOSITORY_BASE}/${REPO_NAME}:$IMAGE_TAG
    finally:
      - echo Done building
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push ${REPOSITORY_BASE}/${REPO_NAME}:$COMMIT_TAG
      - docker push ${REPOSITORY_BASE}/${REPO_NAME}:$IMAGE_TAG

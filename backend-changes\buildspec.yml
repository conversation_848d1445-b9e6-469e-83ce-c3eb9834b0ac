version: 0.2

phases:
  pre_build:
    commands:
      - echo Logging in to Amazon ECR...
      - aws --version
      - BUILD_ACCOUNT_ID=$(echo $CODEBUILD_BUILD_ARN | cut -f 5 -d ":")
      - BUILD_REGION_ID=$(echo $CODEBUILD_BUILD_ARN | cut -f 4 -d ":")
      - ACCOUNT_ID=${ACCOUNT_ID:-$BUILD_ACCOUNT_ID}
      - REGION_ID=${REGION_ID:-$BUILD_REGION_ID}
      - REPOSITORY_BASE=${REPOSITORY_BASE:-$ACCOUNT_ID.dkr.ecr.$REGION_ID.amazonaws.com}
      - REPO=${REPOSITORY_BASE}/
      - aws ecr get-login-password --region $REGION_ID | docker login --username AWS --password-stdin $REPOSITORY_BASE
      - BACKEND_REPO_NAME=${BACKEND_REPO_NAME:-lecet_${BUILD_SERVICE:-backend}}
      - CELERY_REPO_NAME=${CELERY_REPO_NAME:-lecet_celery}
      - CELERY_BEAT_REPO_NAME=${CELERY_BEAT_REPO_NAME:-lecet_celery-beat}
      - FLOWER_REPO_NAME=${FLOWER_REPO_NAME:-lecet_flower}
      - COMMIT_HASH=$(echo $CODEBUILD_RESOLVED_SOURCE_VERSION | cut -c 1-7)
      - IMAGE_TAG=${IMAGE_TAG:-$(echo $CODEBUILD_SOURCE_VERSION | sed 's/[^a-zA-Z0-9]/-/g')}
      - COMMIT_TAG=${COMMIT_HASH:=$IMAGE_TAG}
    finally:
      - echo "Done prebuilding $IMAGE_TAG ($COMMIT_TAG)"
  build:
    commands:
      - echo Build started on `date`
      - echo Building the Docker image...
      - envsubst < .env.example > .env
      - docker-compose build ${BUILD_SERVICE}
      - docker tag ${REPO}${BACKEND_REPO_NAME}:latest ${REPOSITORY_BASE}/${BACKEND_REPO_NAME}:$COMMIT_TAG
      - docker tag ${REPOSITORY_BASE}/${BACKEND_REPO_NAME}:$COMMIT_TAG ${REPOSITORY_BASE}/${BACKEND_REPO_NAME}:$IMAGE_TAG
      - |
        if [ -z "$BUILD_SERVICE" ]; then
          docker tag ${REPO}${CELERY_REPO_NAME}:latest ${REPOSITORY_BASE}/${CELERY_REPO_NAME}:$COMMIT_TAG
          docker tag ${REPOSITORY_BASE}/${CELERY_REPO_NAME}:$COMMIT_TAG ${REPOSITORY_BASE}/${CELERY_REPO_NAME}:$IMAGE_TAG
          docker tag ${REPO}${CELERY_BEAT_REPO_NAME}:latest ${REPOSITORY_BASE}/${CELERY_BEAT_REPO_NAME}:$COMMIT_TAG
          docker tag ${REPOSITORY_BASE}/${CELERY_BEAT_REPO_NAME}:$COMMIT_TAG ${REPOSITORY_BASE}/${CELERY_BEAT_REPO_NAME}:$IMAGE_TAG
          docker tag ${REPO}${FLOWER_REPO_NAME}:latest ${REPOSITORY_BASE}/${FLOWER_REPO_NAME}:$COMMIT_TAG
          docker tag ${REPOSITORY_BASE}/${FLOWER_REPO_NAME}:$COMMIT_TAG ${REPOSITORY_BASE}/${FLOWER_REPO_NAME}:$IMAGE_TAG
        fi
    finally:
      - echo Done building
  post_build:
    commands:
      - echo Build completed on `date`
      - echo Pushing the Docker images...
      - docker push ${REPOSITORY_BASE}/${BACKEND_REPO_NAME}:$COMMIT_TAG
      - docker push ${REPOSITORY_BASE}/${BACKEND_REPO_NAME}:$IMAGE_TAG
      - |
        if [ -z "$BUILD_SERVICE" ]; then
          docker push ${REPOSITORY_BASE}/${CELERY_REPO_NAME}:$COMMIT_TAG
          docker push ${REPOSITORY_BASE}/${CELERY_REPO_NAME}:$IMAGE_TAG
          docker push ${REPOSITORY_BASE}/${CELERY_BEAT_REPO_NAME}:$COMMIT_TAG
          docker push ${REPOSITORY_BASE}/${CELERY_BEAT_REPO_NAME}:$IMAGE_TAG
          docker push ${REPOSITORY_BASE}/${FLOWER_REPO_NAME}:$COMMIT_TAG
          docker push ${REPOSITORY_BASE}/${FLOWER_REPO_NAME}:$IMAGE_TAG
        fi

# CloudWatch Log Groups for CodeBuild

resource "aws_cloudwatch_log_group" "backend_base_build_logs" {
  name = "/aws/codebuild/one-off-backend-base-build"

}

resource "aws_cloudwatch_log_group" "backend_build_logs" {
  name = "/aws/codebuild/new-staging-backend-build"

}

resource "aws_cloudwatch_log_group" "web_build_logs" {
  name = "/aws/codebuild/new-staging-web-build"

}

# CloudWatch Log Groups for CodeDeploy
resource "aws_cloudwatch_log_group" "backend_deploy_logs" {
  name = "/aws/codebuild/new-staging-backend-deploy"

}

resource "aws_cloudwatch_log_group" "web_deploy_logs" {
  name = "/aws/codebuild/new-staging-web-deploy"

}
